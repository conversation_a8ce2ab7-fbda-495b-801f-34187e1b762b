package com.zsmall.activity.biz.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 活动过期服务管理器
 * 根据配置选择不同的延迟消息实现方式
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityExpireServiceManager {

    private final ActivityExpireDelayService activityExpireDelayService;
    private final ActivityExpireMultiQueueService activityExpireMultiQueueService;
    private final ActivityExpireRedisService activityExpireRedisService;

    /**
     * 活动过期机制配置
     * 可选值: delay(延迟插件) | ttl(TTL+死信队列) | redis(Redis延时队列) | multi(多队列TTL)
     */
    @Value("${activity.expiration.mechanism:delay}")
    private String expirationMechanism;

    /**
     * 发送活动过期延时消息
     * 根据配置选择合适的实现方式
     *
     * @param activeCode 活动编码
     * @param type       活动类型 1-供应商活动 2-分销商活动
     * @param endTime    活动结束时间
     */
    public void sendActivityExpireMessage(String activeCode, Integer type, Date endTime) {
        try {
            switch (expirationMechanism.toLowerCase()) {
                case "delay":
                    // 使用延迟消息插件（推荐）
                    activityExpireDelayService.sendActivityExpireMessage(activeCode, type, endTime);
                    break;
                case "multi":
                    // 使用多队列TTL方案
                    activityExpireMultiQueueService.sendActivityExpireMessage(activeCode, type, endTime);
                    break;
                case "redis":
                    // 使用Redis延时队列
                    activityExpireRedisService.sendActivityExpireMessage(activeCode, type, endTime);
                    break;
                case "ttl":
                default:
                    // 默认使用延迟消息插件
                    log.warn("使用默认的延迟消息插件方式，建议配置activity.expiration.mechanism=delay");
                    activityExpireDelayService.sendActivityExpireMessage(activeCode, type, endTime);
                    break;
            }
            
            log.debug("活动过期消息发送完成: 活动编码={}, 类型={}, 使用方式={}", 
                    activeCode, type, expirationMechanism);
        } catch (Exception e) {
            log.error("发送活动过期消息失败: 活动编码={}, 使用方式={}", 
                    activeCode, expirationMechanism, e);
            throw e;
        }
    }

    /**
     * 批量发送活动过期延时消息
     *
     * @param expireMessages 过期消息列表
     */
    public void sendBatchActivityExpireMessage(java.util.List<ActivityExpireMessage> expireMessages) {
        if (expireMessages == null || expireMessages.isEmpty()) {
            return;
        }

        for (ActivityExpireMessage message : expireMessages) {
            sendActivityExpireMessage(message.getActiveCode(), message.getType(), message.getEndTime());
        }
    }

    /**
     * 获取当前使用的过期机制
     */
    public String getCurrentMechanism() {
        return expirationMechanism;
    }

    /**
     * 活动过期消息实体
     */
    public static class ActivityExpireMessage {
        private String activeCode;
        private Integer type;
        private Date endTime;

        public ActivityExpireMessage() {}

        public ActivityExpireMessage(String activeCode, Integer type, Date endTime) {
            this.activeCode = activeCode;
            this.type = type;
            this.endTime = endTime;
        }

        // Getters and Setters
        public String getActiveCode() {
            return activeCode;
        }

        public void setActiveCode(String activeCode) {
            this.activeCode = activeCode;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public Date getEndTime() {
            return endTime;
        }

        public void setEndTime(Date endTime) {
            this.endTime = endTime;
        }
    }
}
