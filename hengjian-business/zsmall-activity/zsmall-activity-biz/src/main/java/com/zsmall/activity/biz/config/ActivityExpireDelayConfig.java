package com.zsmall.activity.biz.config;

import com.hengjian.stream.mq.constant.RabbitMqConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * 活动过期延迟消息配置类
 * 基于RabbitMQ延迟消息插件实现
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Slf4j
@Configuration
public class ActivityExpireDelayConfig {

    /**
     * 活动过期延迟交换机
     * 使用x-delayed-message类型，需要安装rabbitmq_delayed_message_exchange插件
     */
    @Bean
    public CustomExchange activityExpireDelayExchange() {
        Map<String, Object> arguments = new HashMap<>();
        arguments.put("x-delayed-type", "direct");
        
        return new CustomExchange(
            RabbitMqConstant.ACTIVITY_EXPIRE_DELAY_EXCHANGE,
            "x-delayed-message",
            true,
            false,
            arguments
        );
    }

    /**
     * 活动过期处理队列
     */
    @Bean
    public Queue activityExpireProcessQueue() {
        return QueueBuilder
            .durable(RabbitMqConstant.ACTIVITY_EXPIRE_PROCESS_QUEUE)
            .build();
    }

    /**
     * 绑定延迟交换机到处理队列
     */
    @Bean
    public Binding activityExpireDelayBinding() {
        return BindingBuilder
            .bind(activityExpireProcessQueue())
            .to(activityExpireDelayExchange())
            .with(RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY)
            .noargs();
    }

    /**
     * 检查延迟消息插件是否安装
     * 在应用启动时进行检查
     */
    @Bean
    public DelayPluginChecker delayPluginChecker() {
        return new DelayPluginChecker();
    }

    /**
     * 延迟插件检查器
     */
    public static class DelayPluginChecker {
        public DelayPluginChecker() {
            log.info("=== RabbitMQ延迟消息插件配置检查 ===");
            log.info("请确保已安装rabbitmq_delayed_message_exchange插件");
            log.info("安装命令: rabbitmq-plugins enable rabbitmq_delayed_message_exchange");
            log.info("如果插件未安装，延迟消息功能将无法正常工作");
            log.info("=======================================");
        }
    }
}
