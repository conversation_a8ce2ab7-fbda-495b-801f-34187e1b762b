package com.zsmall.activity.biz.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.hengjian.stream.mq.constant.RabbitMqConstant;
import com.zsmall.activity.entity.domain.dto.productActivity.ActivityExpireMq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 基于RabbitMQ延迟消息插件的活动过期服务
 * 解决TTL队列消息阻塞问题，使用延迟消息插件实现精确延迟
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityExpireDelayService {

    private final RabbitTemplate rabbitTemplate;

    /**
     * 发送活动过期延时消息（延迟消息插件方案）
     *
     * @param activeCode 活动编码
     * @param type       活动类型 1-供应商活动 2-分销商活动
     * @param endTime    活动结束时间
     */
    public void sendActivityExpireMessage(String activeCode, Integer type, Date endTime) {
        try {
            // 计算延时时间
            long currentTime = System.currentTimeMillis();
            long endTimeMillis = endTime.getTime();
            long delayTime = endTimeMillis - currentTime;

            if (delayTime <= 0) {
                log.warn("活动已过期，无需发送延时消息: 活动编码={}, 结束时间={}", activeCode, DateUtil.formatDateTime(endTime));
                return;
            }

            // 构造消息体
            ActivityExpireMq mq = new ActivityExpireMq();
            mq.setActiveCode(activeCode);
            mq.setType(type);
            String messageBody = JSONUtil.toJsonStr(mq);

            // 发送延迟消息到延迟交换机
            rabbitTemplate.convertAndSend(
                RabbitMqConstant.ACTIVITY_EXPIRE_DELAY_EXCHANGE,
                RabbitMqConstant.ACTIVITY_EXPIRE_ROUTING_KEY,
                messageBody,
                message -> {
                    // 设置延迟时间（毫秒）
                    message.getMessageProperties().setDelay(Math.toIntExact(delayTime));
                    return message;
                }
            );

            log.info("发送活动过期延时消息成功(延迟插件): 活动编码={}, 活动类型={}, 到期时间={}, 延时={}ms", 
                    activeCode, type, DateUtil.formatDateTime(endTime), delayTime);
        } catch (Exception e) {
            log.error("发送活动过期消息失败(延迟插件): 活动编码={}", activeCode, e);
        }
    }

    /**
     * 批量发送活动过期延时消息
     *
     * @param expireMessages 过期消息列表
     */
    public void sendBatchActivityExpireMessage(java.util.List<ActivityExpireMessage> expireMessages) {
        if (expireMessages == null || expireMessages.isEmpty()) {
            log.warn("批量发送活动过期消息：消息列表为空");
            return;
        }

        for (ActivityExpireMessage expireMessage : expireMessages) {
            sendActivityExpireMessage(
                expireMessage.getActiveCode(),
                expireMessage.getType(),
                expireMessage.getEndTime()
            );
        }

        log.info("批量发送活动过期延时消息完成，共发送{}条消息", expireMessages.size());
    }

    /**
     * 取消活动过期延时消息
     * 注意：延迟消息插件不支持取消已发送的延迟消息
     * 如需取消功能，建议在消费端进行状态检查
     *
     * @param activeCode 活动编码
     * @param type       活动类型
     */
    public void cancelActivityExpireMessage(String activeCode, Integer type) {
        log.warn("延迟消息插件不支持取消已发送的延迟消息，请在消费端进行状态检查: 活动编码={}, 类型={}", 
                activeCode, type);
    }

    /**
     * 活动过期消息实体
     */
    public static class ActivityExpireMessage {
        private String activeCode;
        private Integer type;
        private Date endTime;

        public ActivityExpireMessage() {}

        public ActivityExpireMessage(String activeCode, Integer type, Date endTime) {
            this.activeCode = activeCode;
            this.type = type;
            this.endTime = endTime;
        }

        // Getters and Setters
        public String getActiveCode() {
            return activeCode;
        }

        public void setActiveCode(String activeCode) {
            this.activeCode = activeCode;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public Date getEndTime() {
            return endTime;
        }

        public void setEndTime(Date endTime) {
            this.endTime = endTime;
        }
    }
}
